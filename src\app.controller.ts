import {
  <PERSON>,
  Get,
  MessageEvent,
  Param,
  Query,
  <PERSON><PERSON>,
  Sse,
} from '@nestjs/common';
import { AppService } from './app.service';
import { Response } from 'express';
import { readFileSync } from 'fs';
import { join } from 'path';
import { cwd } from 'process';
import { Observable } from 'rxjs';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  index(@Res() response: Response) {
    const index = readFileSync(join(cwd(), 'public/index.html')).toString();
    response.type('text/html').send(index);
  }

  @Sse('/api/v1/sse')
  progress(@Query('streamId') streamId: string): Observable<MessageEvent> {
    console.log('🚀 ~ AppController ~ progress ~ streamId:', streamId);
    return this.appService.processLongTask(streamId);
  }
}
