<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SSE Progress Example</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.2/css/bulma.min.css">
</head>
<main class="is-fullheight">

  <body <section class="section">
    <h1 class="title is-1 has-text-centered">Server-sent events</h1>
    <section class="section">
      <h2 class="subtitle has-text-centered">
        With server-sent events, it's possible for a server to send new data to a web page at any
        time, by pushing messages to the web page. These incoming messages can be treated as <strong>Events</strong> +
        data inside the
        web page.
      </h2>
    </section>
    <div class="box">
      <div class="container">
        <div class="field">
          <label class="label">Press Submit to start receiving events</label>
        </div>
        <div class="field">
          <div class="control has-text-centered">
            <button class="button is-danger mr-2" onclick="handleCancel()">Cancel Request</button>
            <button class="button is-dark" onclick="handleSubmit()">Submit Request</button>
          </div>
        </div>
        <progress id="progress-bar" class="progress is-large is-hidden" value="0" max="100"></progress>
        <div id="notifications" class="mt-5"></div>
      </div>
    </div>
    </section>
  </body>
  <footer class="footer mt-auto">
    <div class="content has-text-centered">
      <p>
        <strong>Made</strong> with ❤️. The source code is avaliable in this <a
          href="https://github.com/diegodario88/server-sent-events">repository</a>
      </p>
    </div>
  </footer>
</main>

<style>
  @keyframes fadeOut {
    from {
      opacity: 1;
    }

    to {
      opacity: 0;
    }
  }

  .notification.fade-out {
    animation: fadeOut 1s ease-in-out forwards;
  }
</style>

<script type="text/javascript">
  let sse = null;
  let sse2 = null;
  const progressBar = document.getElementById('progress-bar');
  const messageList = document.getElementById('message-list');
  const notificationsContainer = document.getElementById('notifications');

  ss2 = new EventSource(`/api/v1/sse2`);

  ss2.onmessage = (event) => {
    console.log('SSE2 data:', event.data);
  };



  const handleSubmit = () => {
    if (sse) {
      sse.close();
    }

    progressBar.value = 0;
    progressBar.classList.remove('is-hidden');
    const customId = Math.floor(Math.random() * 9) + 1;
    sse = new EventSource(`/api/v1/sse?streamId=${encodeURIComponent(customId)}`);

    sse.onmessage = (event) => {
      console.log('Received message:', event.data);

      if (event.data === '\n\n') {
        sse.close();
        return;
      }

      const {progress, description} = JSON.parse(event.data);

      progressBar.value = progress;

      const notification = document.createElement('div');
      notification.classList.add('notification');
      notification.setAttribute('id', event.lastEventId);
      notification.innerHTML = `
        <button class="delete" onclick="this.parentNode.remove()"></button>
        ${description} (${progress}%)
      `;

      notificationsContainer.appendChild(notification);

      setTimeout(() => notification.classList.add('fade-out'), 1000);
      setTimeout(() => notification.remove(), 2000)
    };

    sse.onerror = (err) => {
      progressBar.classList.add("is-hidden")
      const notification = document.createElement('div');
      notification.classList.add('notification');
      notification.classList.add('is-success');
      notification.classList.add('is-light');
      notification.innerHTML = `
        <button class="delete" onclick="this.parentNode.remove()"></button>
        Job has been completed successfully
      `;

      notificationsContainer.appendChild(notification);

      sse.close();
    };

    sse.onclose = () => {
      console.log('Connection was closed');
      progressBar.classList.add("is-hidden")
      successModal.classList.toggle('is-active');
      sse.close();
    };
  };

  const handleCancel = () => {
    if (sse) {
      sse.close();
      sse = null;
      progressBar.classList.add('is-hidden');
    }
  };
</script>

</html>
